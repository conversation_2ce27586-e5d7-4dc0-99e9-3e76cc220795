import { NextRequest, NextResponse } from "next/server";
import { createStripe, getPlanTypeByPriceId } from "@/lib/stripe";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import {
  createSubscription,
  updateSubscriptionStatus,
  getSubscriptionByStripeId,
  getUserByEmail,
  getUserActiveSubscription,
  handleSubscriptionCredits,
  handleSubscriptionRenewal,
} from "@/lib/subscriptions";
import Stripe from "stripe";

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取 Cloudflare Workers 环境变量
    const { env } = await getCloudflareContext();
    if (!env) {
      console.error("Webhook: 环境配置错误");
      return NextResponse.json({ error: "环境配置错误" }, { status: 500 });
    }

    // 获取请求体
    const body = await request.text();
    const signature = request.headers.get("stripe-signature");

    if (!signature) {
      console.error("Webhook: 缺少 Stripe 签名");
      return NextResponse.json({ error: "缺少签名" }, { status: 400 });
    }

    const webhookSecret = env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error("Webhook: STRIPE_WEBHOOK_SECRET 未配置");
      return NextResponse.json({ error: "Webhook 配置错误" }, { status: 500 });
    }

    // 创建 Stripe 实例
    const stripe = createStripe(env);

    let event: Stripe.Event;

    try {
      // 验证 Webhook 签名
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error("Webhook: 签名验证失败:", err);
      return NextResponse.json({ error: "签名验证失败" }, { status: 400 });
    }

    console.log("Webhook 事件类型:", event.type);

    // 处理不同类型的事件
    switch (event.type) {
      case "checkout.session.completed":
        const session = event.data.object as Stripe.Checkout.Session;
        console.log("订阅完成:", session.id);
        console.log("Customer ID:", session.customer);
        console.log("Success URL:", session.success_url);
        console.log("支付状态:", session.payment_status);
        console.log("Session 状态:", session.status);

        // 如果支付成功，处理订阅创建
        if (
          session.status === "complete" &&
          session.payment_status === "paid" &&
          session.mode === "subscription"
        ) {
          console.log("✅ 支付成功完成，开始处理订阅创建");

          try {
            // 获取客户邮箱
            const customerEmail = session.customer_details?.email;
            if (!customerEmail) {
              console.error("❌ 无法获取客户邮箱");
              break;
            }

            // 根据邮箱查找用户
            const user = await getUserByEmail(customerEmail);
            if (!user) {
              console.error(`❌ 找不到邮箱为 ${customerEmail} 的用户`);
              break;
            }

            console.log(`✅ 找到用户: ${user.id} (${customerEmail})`);

            // 检查用户是否已有活跃订阅（用于判断是否为升级）
            const existingSubscription = await getUserActiveSubscription(
              user.id
            );
            const isUpgrade = !!existingSubscription;

            if (isUpgrade) {
              console.log(
                `🔄 检测到订阅升级，现有订阅: ${existingSubscription.planType}`
              );
            }

            // 获取订阅信息 - 需要从 session 中获取 subscription ID
            if (session.subscription) {
              const stripe = createStripe(env);

              console.log(
                "🔍 获取订阅信息，subscription ID:",
                session.subscription
              );

              const stripeSubscription = await stripe.subscriptions.retrieve(
                session.subscription as string
              );

              // 调试：打印完整的订阅对象
              console.log("📋 完整的 Stripe 订阅对象:");
              console.log(JSON.stringify(stripeSubscription, null, 2));

              const priceId = stripeSubscription.items.data[0]?.price.id;
              if (!priceId) {
                console.error("❌ 无法获取价格 ID");
                break;
              }

              const planType = getPlanTypeByPriceId(priceId, env);
              if (!planType) {
                console.error(`❌ 无法识别计划类型，价格 ID: ${priceId}`);
                break;
              }

              // 从订阅项目中获取时间戳信息
              const subscriptionItem = stripeSubscription.items.data[0];
              if (!subscriptionItem) {
                console.error("❌ 找不到订阅项目");
                break;
              }

              console.log("Stripe 时间戳信息 (从订阅项目获取):");
              console.log(
                "current_period_start:",
                subscriptionItem.current_period_start
              );
              console.log(
                "current_period_end:",
                subscriptionItem.current_period_end
              );
              console.log("订阅状态:", stripeSubscription.status);

              // 检查时间戳是否存在
              if (
                !subscriptionItem.current_period_start ||
                !subscriptionItem.current_period_end
              ) {
                console.error("❌ 订阅项目缺少时间戳信息");
                console.error(
                  "current_period_start:",
                  subscriptionItem.current_period_start
                );
                console.error(
                  "current_period_end:",
                  subscriptionItem.current_period_end
                );
                break;
              }

              // 安全地创建日期对象
              const periodStart = new Date(
                subscriptionItem.current_period_start * 1000
              );
              const periodEnd = new Date(
                subscriptionItem.current_period_end * 1000
              );

              console.log("转换后的日期:");
              console.log("periodStart:", periodStart);
              console.log("periodEnd:", periodEnd);
              console.log("periodStart.getTime():", periodStart.getTime());
              console.log("periodEnd.getTime():", periodEnd.getTime());

              // 如果是升级，先取消旧订阅
              if (isUpgrade && existingSubscription) {
                await updateSubscriptionStatus(
                  existingSubscription.stripeSubscriptionId,
                  "canceled"
                );
                console.log(
                  `✅ 旧订阅已取消: ${existingSubscription.stripeSubscriptionId}`
                );
              }

              // 创建订阅记录
              const subscriptionRecord = await createSubscription({
                userId: user.id,
                stripeCustomerId: session.customer as string,
                stripeSubscriptionId: stripeSubscription.id,
                stripePriceId: priceId,
                planType,
                status: stripeSubscription.status,
                currentPeriodStart: periodStart,
                currentPeriodEnd: periodEnd,
                env,
              });

              console.log(`✅ 订阅记录已创建: ${subscriptionRecord.id}`);

              // 发放积分（传递升级标识）
              await handleSubscriptionCredits(
                user.id,
                planType,
                env,
                isUpgrade
              );

              console.log(
                `🎉 订阅处理完成！用户 ${user.id} 已获得 ${planType} 计划的积分`
              );
            }
          } catch (error) {
            console.error("❌ 处理订阅创建失败:", error);
          }
        }

        break;

      case "customer.subscription.created":
        const subscription = event.data.object as Stripe.Subscription;
        console.log("订阅创建:", subscription.id);

        // 获取价格 ID 并确定计划类型
        const priceId = subscription.items.data[0]?.price.id;
        if (priceId) {
          const planType = getPlanTypeByPriceId(priceId, env);
          console.log("订阅计划:", planType);

          // 注意：订阅创建事件通常在 checkout.session.completed 之后触发
          // 主要的业务逻辑应该在 checkout.session.completed 中处理
          console.log(
            "ℹ️ 订阅创建事件已记录，主要处理逻辑在 checkout.session.completed 中"
          );
        }

        break;

      case "invoice.payment_succeeded":
        const invoice = event.data.object as Stripe.Invoice;
        console.log("支付成功:", invoice.id);

        // 处理定期付款成功（续费）
        if (
          invoice.subscription &&
          invoice.billing_reason === "subscription_cycle"
        ) {
          console.log("🔄 处理订阅续费:", invoice.subscription);

          try {
            const subscriptionRecord = await getSubscriptionByStripeId(
              invoice.subscription as string
            );

            if (subscriptionRecord) {
              // 续费时重新发放积分
              await handleSubscriptionRenewal(
                subscriptionRecord.userId,
                subscriptionRecord.planType as any,
                env
              );

              console.log(
                `🎉 续费处理完成！用户 ${subscriptionRecord.userId} 的积分已重置`
              );
            } else {
              console.warn(`⚠️ 找不到订阅记录: ${invoice.subscription}`);
            }
          } catch (error) {
            console.error("❌ 处理续费失败:", error);
          }
        }

        break;

      case "customer.subscription.updated":
        const updatedSubscription = event.data.object as Stripe.Subscription;
        console.log("订阅更新:", updatedSubscription.id);

        try {
          // 更新订阅状态
          const updated = await updateSubscriptionStatus(
            updatedSubscription.id,
            updatedSubscription.status,
            new Date(updatedSubscription.current_period_start * 1000),
            new Date(updatedSubscription.current_period_end * 1000)
          );

          if (updated) {
            console.log(
              `✅ 订阅状态已更新: ${updatedSubscription.id} -> ${updatedSubscription.status}`
            );
          } else {
            console.warn(
              `⚠️ 找不到要更新的订阅记录: ${updatedSubscription.id}`
            );
          }
        } catch (error) {
          console.error("❌ 更新订阅状态失败:", error);
        }

        break;

      case "customer.subscription.deleted":
        const deletedSubscription = event.data.object as Stripe.Subscription;
        console.log("订阅取消:", deletedSubscription.id);

        try {
          // 更新订阅状态为已取消
          const updated = await updateSubscriptionStatus(
            deletedSubscription.id,
            "canceled"
          );

          if (updated) {
            console.log(`✅ 订阅已标记为取消: ${deletedSubscription.id}`);
            console.log("ℹ️ 用户的剩余积分将保留，不会被清零");
          } else {
            console.warn(
              `⚠️ 找不到要取消的订阅记录: ${deletedSubscription.id}`
            );
          }
        } catch (error) {
          console.error("❌ 处理订阅取消失败:", error);
        }

        break;

      default:
        console.log("未处理的事件类型:", event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook 处理错误:", error);
    return NextResponse.json({ error: "Webhook 处理失败" }, { status: 500 });
  }
}
