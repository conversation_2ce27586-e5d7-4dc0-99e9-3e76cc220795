import { NextRequest, NextResponse } from "next/server";
import { createStripe, getPlanTypeByPriceId } from "@/lib/stripe";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import Stripe from "stripe";

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取 Cloudflare Workers 环境变量
    const { env } = await getCloudflareContext();
    if (!env) {
      console.error("Webhook: 环境配置错误");
      return NextResponse.json({ error: "环境配置错误" }, { status: 500 });
    }

    // 获取请求体
    const body = await request.text();
    const signature = request.headers.get("stripe-signature");

    if (!signature) {
      console.error("Webhook: 缺少 Stripe 签名");
      return NextResponse.json({ error: "缺少签名" }, { status: 400 });
    }

    const webhookSecret = env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error("Webhook: STRIPE_WEBHOOK_SECRET 未配置");
      return NextResponse.json({ error: "Webhook 配置错误" }, { status: 500 });
    }

    // 创建 Stripe 实例
    const stripe = createStripe(env);

    let event: Stripe.Event;

    try {
      // 验证 Webhook 签名
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error("Webhook: 签名验证失败:", err);
      return NextResponse.json({ error: "签名验证失败" }, { status: 400 });
    }

    console.log("Webhook 事件类型:", event.type);

    // 处理不同类型的事件
    switch (event.type) {
      case "checkout.session.completed":
        const session = event.data.object as Stripe.Checkout.Session;
        console.log("订阅完成:", session.id);
        console.log("Customer ID:", session.customer);
        console.log("Success URL:", session.success_url);
        console.log("支付状态:", session.payment_status);
        console.log("Session 状态:", session.status);

        // 如果支付成功，记录 session 信息
        if (
          session.status === "complete" &&
          session.payment_status === "paid"
        ) {
          console.log("✅ 支付成功完成，客户应该已经被重定向到成功页面");
        }

        // 这里可以添加订阅成功后的业务逻辑
        // 比如：更新用户订阅状态、记录到数据库等

        break;

      case "customer.subscription.created":
        const subscription = event.data.object as Stripe.Subscription;
        console.log("订阅创建:", subscription.id);

        // 获取价格 ID 并确定计划类型
        const priceId = subscription.items.data[0]?.price.id;
        if (priceId) {
          const planType = getPlanTypeByPriceId(priceId, env);
          console.log("订阅计划:", planType);
        }

        break;

      case "invoice.payment_succeeded":
        const invoice = event.data.object as Stripe.Invoice;
        console.log("支付成功:", invoice.id);

        // 处理定期付款成功

        break;

      case "customer.subscription.updated":
        const updatedSubscription = event.data.object as Stripe.Subscription;
        console.log("订阅更新:", updatedSubscription.id);

        break;

      case "customer.subscription.deleted":
        const deletedSubscription = event.data.object as Stripe.Subscription;
        console.log("订阅取消:", deletedSubscription.id);

        break;

      default:
        console.log("未处理的事件类型:", event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook 处理错误:", error);
    return NextResponse.json({ error: "Webhook 处理失败" }, { status: 500 });
  }
}
