import { NextRequest, NextResponse } from "next/server";
import { createStripe } from "@/lib/stripe";
import { getCloudflareContext } from "@opennextjs/cloudflare";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = request.nextUrl;
    const sessionId = searchParams.get("session_id");

    if (!sessionId) {
      return NextResponse.json({ error: "缺少 session_id" }, { status: 400 });
    }

    // 获取 Cloudflare Workers 环境变量
    const { env } = await getCloudflareContext();
    if (!env) {
      return NextResponse.json({ error: "环境配置错误" }, { status: 500 });
    }

    // 创建 Stripe 实例
    const stripe = createStripe(env);

    // 获取 session 信息
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    return NextResponse.json({
      status: session.status,
      payment_status: session.payment_status,
      customer_email: session.customer_details?.email,
      success_url: session.success_url,
      should_redirect:
        session.status === "complete" && session.payment_status === "paid",
    });
  } catch (error) {
    console.error("获取 session 状态失败:", error);
    return NextResponse.json(
      { error: "获取 session 状态失败" },
      { status: 500 }
    );
  }
}
