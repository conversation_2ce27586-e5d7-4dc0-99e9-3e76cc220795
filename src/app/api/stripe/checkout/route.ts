import { NextRequest, NextResponse } from "next/server";
import {
  createStripe,
  getSubscriptionPlans,
  type PlanType,
} from "@/lib/stripe";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { routing } from "@/i18n/routing";

// 从请求中获取用户的语言设置
function getUserLocale(request: NextRequest): string {
  // 1. 首先尝试从请求体中获取 locale（前端传递）
  // 2. 然后尝试从 Referer 头中提取路径的语言信息
  // 3. 最后尝试从 Accept-Language 头中获取
  // 4. 都失败则使用默认语言

  const referer = request.headers.get("referer");
  if (referer) {
    try {
      const url = new URL(referer);
      const pathSegments = url.pathname.split("/").filter(Boolean);
      const firstSegment = pathSegments[0];

      // 检查第一个路径段是否是支持的语言
      if (
        routing.locales.includes(
          firstSegment as (typeof routing.locales)[number]
        )
      ) {
        return firstSegment;
      }
    } catch (error) {
      console.log("解析 referer URL 失败:", error);
    }
  }

  // 从 Accept-Language 头中获取首选语言
  const acceptLanguage = request.headers.get("accept-language");
  if (acceptLanguage) {
    // 解析 Accept-Language 头，格式如: "zh-CN,zh;q=0.9,en;q=0.8"
    const languages = acceptLanguage
      .split(",")
      .map((lang) => lang.split(";")[0].trim())
      .map((lang) => lang.split("-")[0]); // 只取主语言代码

    // 找到第一个支持的语言
    for (const lang of languages) {
      if (routing.locales.includes(lang as (typeof routing.locales)[number])) {
        return lang;
      }
    }
  }

  // 使用默认语言
  return routing.defaultLocale;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取 Cloudflare Workers 环境变量
    const { env } = await getCloudflareContext();
    if (!env) {
      return NextResponse.json({ error: "环境配置错误" }, { status: 500 });
    }

    // 解析请求体
    const body = (await request.json()) as { plan?: string; locale?: string };
    const { plan, locale: requestLocale } = body;

    if (!plan || typeof plan !== "string") {
      return NextResponse.json(
        { error: "缺少有效的订阅计划" },
        { status: 400 }
      );
    }

    // 获取用户的语言设置
    const userLocale = requestLocale || getUserLocale(request);
    console.log("检测到的用户语言:", userLocale);

    // 验证计划类型
    const plans = getSubscriptionPlans(env);
    if (!(plan in plans)) {
      return NextResponse.json({ error: "无效的订阅计划" }, { status: 400 });
    }

    const selectedPlan = plans[plan as PlanType];

    console.log("Selected plan:", selectedPlan);
    console.log("Stripe Price ID:", selectedPlan.stripePriceId);

    // 构建多语言路径
    const getLocalizedPath = (path: string) => {
      if (userLocale === routing.defaultLocale) {
        // 默认语言不需要前缀（根据 localePrefix: "as-needed" 配置）
        return path;
      }
      return `/${userLocale}${path}`;
    };

    // 检查是否有配置 Stripe Price ID，或者配置的是产品 ID 而不是价格 ID
    if (
      !selectedPlan.stripePriceId ||
      selectedPlan.stripePriceId.startsWith("prod_") ||
      !selectedPlan.stripePriceId.startsWith("price_")
    ) {
      const demoSuccessPath = getLocalizedPath(
        `/subscription/success?demo=true&plan=${plan}`
      );
      return NextResponse.json({
        url: `${env.NEXTAUTH_URL}${demoSuccessPath}`,
        demo: true,
        message:
          "演示模式：当前配置的是产品 ID，实际支付需要配置 Stripe 价格 ID (以 price_ 开头)",
      });
    }

    // 创建 Stripe 实例
    const stripe = createStripe(env);

    // 构建成功和取消 URL
    const successPath = getLocalizedPath(`/subscription/success?plan=${plan}`);
    const cancelPath = getLocalizedPath("/");

    // 创建 Stripe Checkout Session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price: selectedPlan.stripePriceId,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${env.NEXTAUTH_URL}${successPath}`,
      cancel_url: `${env.NEXTAUTH_URL}${cancelPath}`,
      metadata: {
        planType: plan,
        locale: userLocale,
      },
    });

    // 记录实际使用的 URL（用于调试）
    console.log("检测语言:", userLocale);
    console.log("基础 URL:", env.NEXTAUTH_URL);
    console.log("成功页面 URL:", `${env.NEXTAUTH_URL}${successPath}`);
    console.log("取消页面 URL:", `${env.NEXTAUTH_URL}${cancelPath}`);

    return NextResponse.json({
      url: checkoutSession.url,
    });
  } catch (error) {
    console.error("Stripe checkout error:", error);
    return NextResponse.json(
      { error: "支付服务暂时不可用，请稍后重试" },
      { status: 500 }
    );
  }
}
