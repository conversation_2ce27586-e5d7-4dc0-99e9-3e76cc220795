import { NextRequest } from "next/server";
import { createAuth, getAuth } from "@/lib/auth";
import { getCloudflareContext } from "@opennextjs/cloudflare";

export async function GET(request: NextRequest) {
  if (process.env.NODE_ENV === "development") {
    // 开发环境下使用本地配置
    const auth = await getAuth();
    return auth.handlers.GET(request);
  } else {
    // 生产环境下使用 Cloudflare 配置
    const { env } = await getCloudflareContext({ async: true });
    const auth = createAuth(env.DB, env);
    return auth.handlers.GET(request);
  }
}

export async function POST(request: NextRequest) {
  if (process.env.NODE_ENV === "development") {
    // 开发环境下使用本地配置
    const auth = await getAuth();
    return auth.handlers.POST(request);
  } else {
    // 生产环境下使用 Cloudflare 配置
    const { env } = await getCloudflareContext({ async: true });
    const auth = createAuth(env.DB, env);
    return auth.handlers.POST(request);
  }
}
