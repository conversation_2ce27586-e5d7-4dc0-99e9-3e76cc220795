import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@/lib/auth";
import { getUserActiveSubscription } from "@/lib/subscriptions";
import { getUserCredits } from "@/lib/credits";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 获取用户会话
    const auth = await getAuth();
    const session = await auth.auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    // 获取用户的活跃订阅
    const subscription = await getUserActiveSubscription(session.user.id);
    
    // 获取用户积分
    const credits = await getUserCredits(session.user.id);

    return NextResponse.json({
      subscription: subscription ? {
        id: subscription.id,
        planType: subscription.planType,
        status: subscription.status,
        creditsPerMonth: subscription.creditsPerMonth,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
      } : null,
      credits,
    });
  } catch (error) {
    console.error("获取用户订阅信息失败:", error);
    return NextResponse.json(
      { error: "获取订阅信息失败" },
      { status: 500 }
    );
  }
}
