import { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "@/i18n/navigation";

interface SuccessContentProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
  locale: string;
}

async function SuccessContent({ searchParams, locale }: SuccessContentProps) {
  const params = await searchParams;
  const planType = params.plan as string;
  const isDemo = params.demo === "true";

  // 启用静态渲染
  setRequestLocale(locale);

  const t = await getTranslations("Subscription.success");
  const planT = await getTranslations("Subscription.plans");

  // 获取本地化的计划名称
  const getPlanDisplayName = (plan: string) => {
    switch (plan) {
      case "basic":
        return planT("basic");
      case "pro":
        return planT("pro");
      case "enterprise":
        return planT("enterprise");
      default:
        return plan?.toUpperCase() || "";
    }
  };

  return (
    <div className="container max-w-2xl mx-auto py-16">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <span className="text-2xl">✓</span>
          </div>
          <CardTitle className="text-3xl text-green-600">
            {isDemo ? t("demoTitle") : t("title")}
          </CardTitle>
          <CardDescription className="text-lg">
            {isDemo ? t("demoMessage") : t("realPaymentMessage")}
          </CardDescription>
          {planType && (
            <Badge variant="secondary" className="mt-2">
              {t("planDetails", { plan: getPlanDisplayName(planType) })}
            </Badge>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="bg-muted p-6 rounded-lg">
            <h3 className="font-semibold mb-2">
              {isDemo
                ? locale === "zh"
                  ? "演示说明"
                  : "Demo Information"
                : locale === "zh"
                ? "接下来会发生什么？"
                : "What happens next?"}
            </h3>
            {isDemo ? (
              <ul className="text-left space-y-2 text-muted-foreground">
                {locale === "zh" ? (
                  <>
                    <li>• 这是一个完整的订阅流程演示</li>
                    <li>• 要启用真实支付，请配置 Stripe 价格 ID</li>
                    <li>• 价格 ID 以 price_ 开头，而非产品 ID (prod_)</li>
                    <li>• 在 Stripe Dashboard 中为每个产品创建价格</li>
                  </>
                ) : (
                  <>
                    <li>• This is a complete subscription flow demo</li>
                    <li>
                      • To enable real payments, configure Stripe price IDs
                    </li>
                    <li>
                      • Price IDs start with price_, not product IDs (prod_)
                    </li>
                    <li>
                      • Create prices for each product in Stripe Dashboard
                    </li>
                  </>
                )}
              </ul>
            ) : (
              <ul className="text-left space-y-2 text-muted-foreground">
                {locale === "zh" ? (
                  <>
                    <li>• 您的积分已发放到账户</li>
                    <li>• 每月1号将自动重新发放积分</li>
                    <li>• 您可以随时在账户设置中管理订阅</li>
                    <li>• 如有问题，请联系客服</li>
                  </>
                ) : (
                  <>
                    <li>• Your credits have been added to your account</li>
                    <li>
                      • Credits will be automatically renewed on the 1st of each
                      month
                    </li>
                    <li>
                      • You can manage your subscription anytime in account
                      settings
                    </li>
                    <li>• Contact support if you have any questions</li>
                  </>
                )}
              </ul>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/">{t("backToHome")}</Link>
            </Button>
            {!isDemo && (
              <Button variant="outline" asChild>
                <Link href="/account">
                  {locale === "zh" ? "查看账户" : "View Account"}
                </Link>
              </Button>
            )}
            {isDemo && (
              <Button variant="outline" asChild>
                <Link href="/">
                  {locale === "zh" ? "重新尝试" : "Try Again"}
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function SubscriptionSuccessPage({
  params,
  searchParams,
}: PageProps) {
  const { locale } = await params;

  return (
    <Suspense
      fallback={
        <div className="container max-w-2xl mx-auto py-16 text-center">
          {locale === "zh" ? "加载中..." : "Loading..."}
        </div>
      }
    >
      <SuccessContent searchParams={searchParams} locale={locale} />
    </Suspense>
  );
}
