"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

interface AutoRedirectProps {
  delay?: number; // 延迟时间（秒）
  targetUrl?: string; // 目标 URL
}

export function AutoRedirect({
  delay = 5,
  targetUrl = "/zh",
}: AutoRedirectProps) {
  const [countdown, setCountdown] = useState(delay);
  const router = useRouter();

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push(targetUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router, targetUrl]);

  return (
    <div className="text-center mt-4">
      <p className="text-sm text-muted-foreground">
        {countdown}秒后自动返回首页...
      </p>
    </div>
  );
}
