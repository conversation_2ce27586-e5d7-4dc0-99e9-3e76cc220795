"use client";

import { SUBSCRIPTION_PLANS } from "@/lib/stripe";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const createCheckoutSession = async (planType: string) => {
  const response = await fetch("/api/stripe/checkout", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ planType }),
  });

  const data = (await response.json()) as {
    checkoutUrl?: string;
    error?: string;
  };

  if (data.checkoutUrl) {
    window.location.href = data.checkoutUrl;
  } else {
    alert("创建支付会话失败");
  }
};

export default function SubscriptionPlansPage() {
  return (
    <div className="container max-w-6xl mx-auto py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">选择订阅计划</h1>
        <p className="text-xl text-muted-foreground">
          选择适合您需求的计划，每月1号重新发放积分
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {Object.entries(SUBSCRIPTION_PLANS).map(([planType, plan]) => (
          <Card key={planType} className="relative">
            {planType === "pro" && (
              <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                推荐
              </Badge>
            )}

            <CardHeader className="text-center">
              <CardTitle className="text-2xl">{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold">${plan.price}</span>
                <span className="text-muted-foreground">/月</span>
              </div>
            </CardHeader>

            <CardContent>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="mr-2">✓</span>
                  每月 {plan.credits.toLocaleString()} 积分
                </li>
                <li className="flex items-center">
                  <span className="mr-2">✓</span>
                  每月1号自动重置积分
                </li>
                <li className="flex items-center">
                  <span className="mr-2">✓</span>
                  24/7 客户支持
                </li>
                <li className="flex items-center">
                  <span className="mr-2">✓</span>
                  随时取消
                </li>
              </ul>
            </CardContent>

            <CardFooter>
              <Button
                className="w-full"
                onClick={() => createCheckoutSession(planType)}
                variant={planType === "pro" ? "default" : "outline"}
              >
                开始订阅
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">常见问题</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
          <div>
            <h3 className="font-semibold mb-2">积分如何使用？</h3>
            <p className="text-muted-foreground">
              积分用于平台内的各项服务，每次使用功能时会消耗相应积分。
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">积分会过期吗？</h3>
            <p className="text-muted-foreground">
              每月1号会重新发放当月积分，上月剩余积分将清零。
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">可以随时取消吗？</h3>
            <p className="text-muted-foreground">
              是的，您可以随时取消订阅。取消后剩余积分仍可继续使用。
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">支付安全吗？</h3>
            <p className="text-muted-foreground">
              我们使用 Stripe 处理支付，符合 PCI DSS 标准，确保支付安全。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
