import React from "react";
import { useTranslations } from "next-intl";
import { setRequestLocale } from "next-intl/server";
import { Link } from "@/i18n/navigation";

export function generateStaticParams() {
  return [{ locale: "en" }, { locale: "zh" }];
}

export default function AboutPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = React.use(params);

  // 启用静态渲染
  setRequestLocale(locale);

  const t = useTranslations("AboutPage");

  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 -mx-4 -mt-8 min-h-[calc(100vh-4rem)] px-4 pt-8">
      <div className="max-w-4xl mx-auto">
        {/* Page Title */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {t("title")}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t("subtitle")}
          </p>
        </div>

        {/* Content Sections */}
        <div className="grid gap-12 md:gap-16">
          {/* Description */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
              {t("description")}
            </p>
          </section>

          {/* Mission */}
          <section className="bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              {t("mission")}
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
              {t("missionText")}
            </p>
          </section>

          {/* Team */}
          <section className="bg-green-50 dark:bg-green-900/20 rounded-2xl p-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              {t("team")}
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
              {t("teamText")}
            </p>
          </section>

          {/* Contact */}
          <section className="bg-purple-50 dark:bg-purple-900/20 rounded-2xl p-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              {t("contact")}
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
              {t("contactText")}
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              {t("backToHome")}
            </Link>
          </section>
        </div>
      </div>
    </div>
  );
}
