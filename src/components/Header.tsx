"use client";

import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { Link } from "@/i18n/navigation";
import LanguageSwitcher from "./LanguageSwitcher";
import { handleSignOut } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { LogOut, User } from "lucide-react";

export default function Header() {
  const nav = useTranslations("Navigation");
  const { data: session, status } = useSession();

  const onSignOut = async () => {
    try {
      await handleSignOut();
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  };

  return (
    <header className="flex justify-between items-center mb-12">
      <div className="flex items-center gap-8">
        <Link
          href="/"
          className="text-2xl font-bold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        >
          My App
        </Link>
        <nav className="flex gap-6">
          <Link
            href="/"
            className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {nav("home")}
          </Link>
          <Link
            href="/about"
            className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {nav("about")}
          </Link>
        </nav>
      </div>
      <div className="flex items-center gap-4">
        {status === "loading" ? (
          <div className="px-4 py-2 text-sm text-gray-500">加载中...</div>
        ) : session?.user ? (
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
              <User size={16} />
              <span>{session.user.name || session.user.email}</span>
            </div>
            <Button
              onClick={onSignOut}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <LogOut size={16} />
              退出登录
            </Button>
          </div>
        ) : (
          <Link
            href="/login"
            className="px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {nav("login")}
          </Link>
        )}
        <LanguageSwitcher />
      </div>
    </header>
  );
}
