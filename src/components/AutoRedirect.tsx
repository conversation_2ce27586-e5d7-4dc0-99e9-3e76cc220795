"use client";

import { useEffect, useState } from "react";
import { useRouter } from "@/i18n/navigation";
import { useLocale } from "next-intl";

interface AutoRedirectProps {
  delay?: number; // 延迟时间（秒）
  targetUrl?: string; // 目标 URL，如果不提供则跳转到首页
}

export function AutoRedirect({ delay = 5, targetUrl }: AutoRedirectProps) {
  const [countdown, setCountdown] = useState(delay);
  const router = useRouter();
  const locale = useLocale();

  // 如果没有提供目标 URL，使用本地化的首页路径
  const finalTargetUrl = targetUrl || "/";

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // 使用多语言路由进行跳转
          router.push(finalTargetUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router, finalTargetUrl]);

  return (
    <div className="text-center mt-4">
      <p className="text-sm text-muted-foreground">
        {locale === "zh"
          ? `${countdown}秒后自动返回首页...`
          : `Auto redirecting to home in ${countdown} seconds...`}
      </p>
    </div>
  );
}
