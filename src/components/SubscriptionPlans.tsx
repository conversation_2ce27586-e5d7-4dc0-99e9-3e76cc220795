"use client";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { motion } from "framer-motion";
import { useState } from "react";
import { Check } from "phosphor-react";
import { useLocale } from "next-intl";

interface Plan {
  name: string;
  price: string;
  description: string;
  features: string[];
  badge?: string;
  value: string;
}

const plans: Plan[] = [
  {
    name: "Basic",
    price: "$9/mo",
    description: "适合个人和初创项目，提供基础功能。",
    features: ["1 项目", "10GB 存储", "社区支持"],
    value: "basic",
  },
  {
    name: "Pro",
    price: "$29/mo",
    description: "适合成长型团队，解锁高级功能。",
    features: ["10 项目", "100GB 存储", "优先支持", "团队协作"],
    badge: "最受欢迎",
    value: "pro",
  },
  {
    name: "Enterprise",
    price: "$99/mo",
    description: "为企业级客户量身定制，支持定制化需求。",
    features: ["不限项目", "1TB+ 存储", "专属客户经理", "SLA 服务保障"],
    value: "enterprise",
  },
];

const cardVariants = {
  hidden: { opacity: 0, y: 40 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { delay: i * 0.15 },
  }),
};

export const SubscriptionPlans = () => {
  const [loading, setLoading] = useState<string | null>(null);
  const locale = useLocale();

  const handleSubscribe = async (plan: Plan) => {
    setLoading(plan.value);
    try {
      console.log("正在发送订阅请求:", { plan: plan.value, locale });
      const res = await fetch("/api/stripe/checkout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          plan: plan.value,
          locale,
        }),
      });

      console.log("API 响应状态:", res.status);

      if (!res.ok) {
        const errorText = await res.text();
        console.error("API 错误响应:", errorText);
        alert(`请求失败 (${res.status}): ${errorText}`);
        return;
      }

      const data: { url?: string; demo?: boolean; message?: string } =
        await res.json();

      console.log("API 响应数据:", data);

      if (data?.demo) {
        // 演示模式：显示提示并跳转到成功页面
        alert(data.message || "演示模式：跳转到成功页面");
        // 如果是演示模式且有 URL，也要跳转
        if (data?.url) {
          console.log("演示模式跳转到:", data.url);
          window.open(data.url, "_self");
        }
      } else if (data?.url) {
        console.log("正在跳转到:", data.url);
        // 尝试多种跳转方法
        try {
          window.open(data.url, "_self");
        } catch (error) {
          console.warn("window.open 失败，尝试 location.href:", error);
          window.location.href = data.url;
        }
      } else {
        console.warn("API 响应中没有 URL 或演示标识:", data);
        alert("响应格式异常，请检查控制台");
      }
    } catch (error) {
      console.error("订阅请求失败:", error);
      alert("支付跳转失败，请稍后重试。");
    } finally {
      setLoading(null);
    }
  };

  return (
    <section className="w-full max-w-5xl mx-auto py-12 px-4 grid grid-cols-1 md:grid-cols-3 gap-6">
      {plans.map((plan, i) => (
        <motion.div
          key={plan.value}
          custom={i}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.5 }}
          variants={cardVariants}
        >
          <Card className="flex flex-col h-full shadow-lg border border-border bg-background/80 dark:bg-background/60">
            <CardHeader>
              <div className="flex items-center gap-2">
                <CardTitle className="text-2xl font-bold">
                  {plan.name}
                </CardTitle>
                {plan.badge && (
                  <Badge className="ml-2" variant="secondary">
                    {plan.badge}
                  </Badge>
                )}
              </div>
              <div className="mt-2 text-3xl font-extrabold text-primary">
                {plan.price}
              </div>
              <CardDescription className="mt-1 text-muted-foreground">
                {plan.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2 mt-4">
                {plan.features.map((feature) => (
                  <li
                    key={feature}
                    className="flex items-center gap-2 text-base"
                  >
                    <Check
                      size={20}
                      className="text-green-500"
                      aria-hidden="true"
                    />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                aria-label={`订阅${plan.name}方案`}
                tabIndex={0}
                disabled={loading === plan.value}
                onClick={() => handleSubscribe(plan)}
              >
                {loading === plan.value ? "正在跳转..." : `立即订阅`}
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      ))}
    </section>
  );
};

export default SubscriptionPlans;
