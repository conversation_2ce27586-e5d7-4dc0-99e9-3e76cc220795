"use client";

import { useLocale, useTranslations } from "next-intl";
import { usePathname, useRouter } from "@/i18n/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function LanguageSwitcher() {
  const t = useTranslations("Navigation");
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();

  const handleLanguageChange = (newLocale: string) => {
    router.replace(pathname, { locale: newLocale });
  };

  return (
    <div>
      <Select value={locale} onValueChange={handleLanguageChange}>
        <SelectTrigger className="w-32">
          <SelectValue aria-label={t("languageLabel")} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="en">{t("switchToEn")}</SelectItem>
          <SelectItem value="zh">{t("switchToZh")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
