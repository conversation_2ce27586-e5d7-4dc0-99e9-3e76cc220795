import NextAuth from "next-auth";
import Google from "next-auth/providers/google";
import { DrizzleAdapter } from "@auth/drizzle-adapter";
import { drizzle } from "drizzle-orm/d1";
import { getDevDB } from "./db";
import * as schema from "./db/schema";

// 创建用于 Cloudflare Workers 环境的 auth 配置
export function createAuth(database: D1Database, env?: CloudflareEnv) {
  // 在 Cloudflare Workers 环境中，环境变量通过 env 参数传递
  const googleClientId = env?.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID;
  const googleClientSecret =
    env?.GOOGLE_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET;

  return NextAuth({
    adapter: DrizzleAdapter(drizzle(database, { schema })),
    trustHost: true, // 信任所有主机，在 Cloudflare Workers 中必需
    providers: [
      Google({
        clientId: googleClientId!,
        clientSecret: googleClientSecret!,
      }),
    ],
    callbacks: {
      session: ({ session, user }) => ({
        ...session,
        user: {
          ...session.user,
          id: user.id,
        },
      }),
    },
  });
}

// 创建用于开发环境的 auth 配置
export function createDevAuth() {
  const db = getDevDB();

  return NextAuth({
    adapter: DrizzleAdapter(db),
    trustHost: true,
    providers: [
      Google({
        clientId: process.env.GOOGLE_CLIENT_ID!,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      }),
    ],
    callbacks: {
      session: ({ session, user }) => ({
        ...session,
        user: {
          ...session.user,
          id: user.id,
        },
      }),
    },
  });
}

// 通用的 auth 获取函数
export async function getAuth() {
  if (process.env.NODE_ENV === "development") {
    return createDevAuth();
  } else {
    // 生产环境下使用 Cloudflare 配置
    const { getCloudflareContext } = await import("@opennextjs/cloudflare");
    const { env } = await getCloudflareContext({ async: true });
    return createAuth(env.DB, env);
  }
}
