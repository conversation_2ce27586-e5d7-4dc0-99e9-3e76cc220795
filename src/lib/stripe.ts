import Stripe from "stripe";

// 在 Cloudflare Workers 中创建 Stripe 实例的函数
export function createStripe(env?: CloudflareEnv) {
  const stripeSecretKey =
    env?.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY;

  if (!stripeSecretKey) {
    throw new Error("STRIPE_SECRET_KEY is not defined");
  }

  return new Stripe(stripeSecretKey, {
    apiVersion: "2025-05-28.basil",
    typescript: true,
    // 在 Cloudflare Workers 环境中使用 fetch API
    httpClient: Stripe.createFetchHttpClient(),
  });
}

// 获取订阅计划配置的函数
export function getSubscriptionPlans(env?: CloudflareEnv) {
  return {
    basic: {
      name: "Basic Plan",
      credits: 1000,
      price: 9.99,
      interval: "month" as const,
      description: "Perfect for getting started",
      stripePriceId:
        env?.STRIPE_BASIC_PRICE_ID || process.env.STRIPE_BASIC_PRICE_ID || "",
    },
    pro: {
      name: "Pro Plan",
      credits: 3000,
      price: 29.99,
      interval: "month" as const,
      description: "Great for regular users",
      stripePriceId:
        env?.STRIPE_PRO_PRICE_ID || process.env.STRIPE_PRO_PRICE_ID || "",
    },
    enterprise: {
      name: "Enterprise Plan",
      credits: 10000,
      price: 99.99,
      interval: "month" as const,
      description: "For power users",
      stripePriceId:
        env?.STRIPE_ENTERPRISE_PRICE_ID ||
        process.env.STRIPE_ENTERPRISE_PRICE_ID ||
        "",
    },
  } as const;
}

// 默认配置（用于开发环境和客户端组件）
export const SUBSCRIPTION_PLANS = getSubscriptionPlans();

export type PlanType = keyof ReturnType<typeof getSubscriptionPlans>;

// 根据 Price ID 获取计划类型
export const getPlanTypeByPriceId = (
  priceId: string,
  env?: CloudflareEnv
): PlanType | null => {
  const plans = getSubscriptionPlans(env);
  for (const [planType, plan] of Object.entries(plans)) {
    if (plan.stripePriceId === priceId) {
      return planType as PlanType;
    }
  }
  return null;
};

// 根据计划类型获取计划信息
export const getPlanByType = (planType: PlanType, env?: CloudflareEnv) => {
  const plans = getSubscriptionPlans(env);
  return plans[planType];
};
