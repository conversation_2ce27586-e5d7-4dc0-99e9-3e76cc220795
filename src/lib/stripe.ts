import Stripe from "stripe";
import { NextRequest } from "next/server";

// 在 Cloudflare Workers 中创建 Stripe 实例的函数
export function createStripe(env?: CloudflareEnv) {
  const stripeSecretKey =
    env?.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY;

  if (!stripeSecretKey) {
    throw new Error("STRIPE_SECRET_KEY is not defined");
  }

  return new Stripe(stripeSecretKey, {
    apiVersion: "2025-05-28.basil",
    typescript: true,
    // 在 Cloudflare Workers 环境中使用 fetch API
    httpClient: Stripe.createFetchHttpClient(),
  });
}

// 获取订阅计划配置的函数
export function getSubscriptionPlans(env?: CloudflareEnv) {
  return {
    basic: {
      name: "Basic Plan",
      credits: 1000,
      price: 9.99,
      interval: "month" as const,
      description: "Perfect for getting started",
      stripePriceId:
        env?.STRIPE_BASIC_PRICE_ID || process.env.STRIPE_BASIC_PRICE_ID || "",
    },
    pro: {
      name: "Pro Plan",
      credits: 3000,
      price: 29.99,
      interval: "month" as const,
      description: "Great for regular users",
      stripePriceId:
        env?.STRIPE_PRO_PRICE_ID || process.env.STRIPE_PRO_PRICE_ID || "",
    },
    enterprise: {
      name: "Enterprise Plan",
      credits: 10000,
      price: 99.99,
      interval: "month" as const,
      description: "For power users",
      stripePriceId:
        env?.STRIPE_ENTERPRISE_PRICE_ID ||
        process.env.STRIPE_ENTERPRISE_PRICE_ID ||
        "",
    },
  } as const;
}

// 默认配置（用于开发环境和客户端组件）
export const SUBSCRIPTION_PLANS = getSubscriptionPlans();

export type PlanType = keyof ReturnType<typeof getSubscriptionPlans>;

// 根据 Price ID 获取计划类型
export const getPlanTypeByPriceId = (
  priceId: string,
  env?: CloudflareEnv
): PlanType | null => {
  const plans = getSubscriptionPlans(env);
  for (const [planType, plan] of Object.entries(plans)) {
    if (plan.stripePriceId === priceId) {
      return planType as PlanType;
    }
  }
  return null;
};

/**
 * 动态获取基础URL，支持不同环境的自动检测
 * @param request NextRequest 对象
 * @param env Cloudflare 环境变量
 * @returns 基础URL字符串
 */
export function getBaseUrl(request: NextRequest, env?: CloudflareEnv): string {
  try {
    // 1. 尝试从请求头动态获取
    const host = request.headers.get("host");

    if (host) {
      // 检测协议：localhost 使用 http，其他使用 https
      const protocol = host.includes("localhost") ? "http" : "https";
      const baseUrl = `${protocol}://${host}`;

      console.log(`动态检测到基础URL: ${baseUrl}`);
      return baseUrl;
    }
  } catch (error) {
    console.warn("动态获取基础URL失败:", error);
  }

  // 2. 兜底使用环境变量
  const fallbackUrl =
    env?.NEXTAUTH_URL || process.env.NEXTAUTH_URL || "http://localhost:3000";
  console.log(`使用兜底URL: ${fallbackUrl}`);
  return fallbackUrl;
}

/**
 * 检测当前运行环境
 * @param host 请求的host头部
 * @returns 环境类型
 */
export function detectEnvironment(
  host?: string
): "development" | "cf-local" | "production" {
  if (!host) return "production";

  if (host.includes("localhost:3000")) return "development";
  if (host.includes("localhost:8787")) return "cf-local";
  return "production";
}

// 根据计划类型获取计划信息
export const getPlanByType = (planType: PlanType, env?: CloudflareEnv) => {
  const plans = getSubscriptionPlans(env);
  return plans[planType];
};
