import { drizzle } from "drizzle-orm/d1";
import { drizzle as drizzleBetterSqlite } from "drizzle-orm/better-sqlite3";
import Database from "better-sqlite3";
import * as schema from "./schema";
import path from "path";
import fs from "fs";

// 获取本地数据库路径的函数
function getLocalDbPath() {
  // 在开发环境下使用固定的数据库文件路径
  const fixedPath = path.join(
    process.cwd(),
    "local-database",
    "ai-next-template.sqlite"
  );

  // 如果固定路径的文件存在且有内容，优先使用
  if (fs.existsSync(fixedPath) && fs.statSync(fixedPath).size > 0) {
    console.log(`🗄️ 使用固定数据库文件: ${path.basename(fixedPath)}`);
    return fixedPath;
  }

  // 否则查找现有的 wrangler 生成的数据库文件
  const dbDir = path.join(
    process.cwd(),
    ".wrangler/state/v3/d1/miniflare-D1DatabaseObject"
  );

  try {
    if (fs.existsSync(dbDir)) {
      // 查找现有的数据库文件
      const files = fs
        .readdirSync(dbDir)
        .filter(
          (file) =>
            file.endsWith(".sqlite") &&
            !file.endsWith(".sqlite-shm") &&
            !file.endsWith(".sqlite-wal") &&
            fs.statSync(path.join(dbDir, file)).size > 0
        );

      if (files.length > 0) {
        const existingDbPath = path.join(dbDir, files[0]);
        console.log(`🔄 迁移现有数据库到固定位置...`);

        // 创建固定目录
        const fixedDir = path.dirname(fixedPath);
        if (!fs.existsSync(fixedDir)) {
          fs.mkdirSync(fixedDir, { recursive: true });
        }

        // 复制现有数据库到固定位置
        fs.copyFileSync(existingDbPath, fixedPath);
        console.log(`✅ 数据库已迁移到: ${path.basename(fixedPath)}`);
        return fixedPath;
      }
    }
  } catch (error) {
    console.warn("迁移数据库时出错:", error);
  }

  // 如果没有现有数据库，使用固定路径
  console.log("🗄️ 创建新的固定数据库文件");
  return fixedPath;
}

// 开发环境下创建本地 SQLite 连接
function createLocalDB() {
  const dbPath = getLocalDbPath();

  // 确保数据库文件存在
  if (!fs.existsSync(dbPath)) {
    // 创建目录
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    // 创建空数据库文件
    fs.writeFileSync(dbPath, "");
    console.log(`📝 创建新的数据库文件: ${path.basename(dbPath)}`);
  }

  const sqlite = new Database(dbPath);
  console.log(`✅ 连接到数据库: ${path.basename(dbPath)}`);
  return drizzleBetterSqlite(sqlite, { schema });
}

// 这个函数将在 better-auth 中使用（生产环境）
export function createDB(database: D1Database) {
  return drizzle(database, { schema });
}

// 开发环境下获取数据库实例
export function getDevDB() {
  if (process.env.NODE_ENV === "development") {
    return createLocalDB();
  }
  throw new Error("getDevDB() 只能在开发环境中使用");
}

// 通用的数据库获取函数
export async function getDB() {
  if (process.env.NODE_ENV === "development") {
    return getDevDB();
  } else {
    // 生产环境下使用 Cloudflare D1
    const { getCloudflareContext } = await import("@opennextjs/cloudflare");
    const { env } = await getCloudflareContext({ async: true });
    return createDB(env.DB);
  }
}

// 导出 schema 以便其他地方使用
export { schema };
