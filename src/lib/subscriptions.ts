import { getDB } from "@/lib/db";
import { subscriptions, users } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { getSubscriptionPlans, type PlanType } from "@/lib/stripe";
import { addUserCredits, resetUserCredits } from "@/lib/credits";
import type { NewSubscription, Subscription } from "@/lib/db/schema";

/**
 * 创建新的订阅记录
 */
export async function createSubscription(data: {
  userId: string;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  planType: PlanType;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  env?: CloudflareEnv;
}): Promise<Subscription> {
  const db = await getDB();
  const plans = getSubscriptionPlans(data.env);
  const plan = plans[data.planType];

  const now = new Date();

  // 确保日期对象有效
  if (
    !data.currentPeriodStart ||
    !data.currentPeriodEnd ||
    typeof data.currentPeriodStart.getTime !== "function" ||
    typeof data.currentPeriodEnd.getTime !== "function"
  ) {
    throw new Error(
      `Invalid date objects: start=${typeof data.currentPeriodStart}, end=${typeof data.currentPeriodEnd}`
    );
  }

  const newSubscription: NewSubscription = {
    userId: data.userId,
    stripeCustomerId: data.stripeCustomerId,
    stripeSubscriptionId: data.stripeSubscriptionId,
    stripePriceId: data.stripePriceId,
    status: data.status,
    planType: data.planType,
    creditsPerMonth: plan.credits,
    currentPeriodStart: data.currentPeriodStart.getTime(),
    currentPeriodEnd: data.currentPeriodEnd.getTime(),
    createdAt: now.getTime(),
    updatedAt: now.getTime(),
  };

  const result = await db
    .insert(subscriptions)
    .values(newSubscription)
    .returning();

  console.log("✅ 订阅记录创建成功:", result[0].id);

  return result[0];
}

/**
 * 更新订阅状态
 */
export async function updateSubscriptionStatus(
  stripeSubscriptionId: string,
  status: string,
  currentPeriodStart?: Date,
  currentPeriodEnd?: Date
): Promise<Subscription | null> {
  const db = await getDB();

  const updateData: Partial<NewSubscription> = {
    status,
    updatedAt: new Date().getTime(),
  };

  if (currentPeriodStart) {
    updateData.currentPeriodStart = currentPeriodStart.getTime();
  }

  if (currentPeriodEnd) {
    updateData.currentPeriodEnd = currentPeriodEnd.getTime();
  }

  const result = await db
    .update(subscriptions)
    .set(updateData)
    .where(eq(subscriptions.stripeSubscriptionId, stripeSubscriptionId))
    .returning();

  return result[0] || null;
}

/**
 * 根据 Stripe 订阅 ID 获取订阅信息
 */
export async function getSubscriptionByStripeId(
  stripeSubscriptionId: string
): Promise<Subscription | null> {
  const db = await getDB();

  const result = await db
    .select()
    .from(subscriptions)
    .where(eq(subscriptions.stripeSubscriptionId, stripeSubscriptionId))
    .limit(1);

  return result[0] || null;
}

/**
 * 根据用户 ID 获取活跃订阅
 */
export async function getUserActiveSubscription(
  userId: string
): Promise<Subscription | null> {
  const db = await getDB();

  const result = await db
    .select()
    .from(subscriptions)
    .where(
      and(eq(subscriptions.userId, userId), eq(subscriptions.status, "active"))
    )
    .limit(1);

  return result[0] || null;
}

/**
 * 根据 Stripe 客户 ID 获取订阅
 */
export async function getSubscriptionByCustomerId(
  stripeCustomerId: string
): Promise<Subscription | null> {
  const db = await getDB();

  const result = await db
    .select()
    .from(subscriptions)
    .where(eq(subscriptions.stripeCustomerId, stripeCustomerId))
    .limit(1);

  return result[0] || null;
}

/**
 * 根据用户邮箱查找用户 ID
 */
export async function getUserByEmail(
  email: string
): Promise<{ id: string } | null> {
  const db = await getDB();

  const result = await db
    .select({ id: users.id })
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  return result[0] || null;
}

/**
 * 处理订阅创建后的积分发放
 */
export async function handleSubscriptionCredits(
  userId: string,
  planType: PlanType,
  env?: CloudflareEnv
): Promise<void> {
  const plans = getSubscriptionPlans(env);
  const plan = plans[planType];

  // 重置用户积分为新订阅的积分数量
  await resetUserCredits(userId, plan.credits);

  console.log(
    `✅ 用户 ${userId} 的积分已重置为 ${plan.credits} (${planType} 计划)`
  );
}

/**
 * 处理订阅续费的积分发放
 */
export async function handleSubscriptionRenewal(
  userId: string,
  planType: PlanType,
  env?: CloudflareEnv
): Promise<void> {
  const plans = getSubscriptionPlans(env);
  const plan = plans[planType];

  // 每月续费时重置积分
  await resetUserCredits(userId, plan.credits);

  console.log(
    `✅ 用户 ${userId} 的积分已续费重置为 ${plan.credits} (${planType} 计划)`
  );
}

/**
 * 删除订阅记录
 */
export async function deleteSubscription(
  stripeSubscriptionId: string
): Promise<boolean> {
  const db = await getDB();

  const result = await db
    .delete(subscriptions)
    .where(eq(subscriptions.stripeSubscriptionId, stripeSubscriptionId))
    .returning();

  return result.length > 0;
}
