import { getDB } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// 获取用户积分
export async function getUserCredits(userId: string): Promise<number> {
  const db = await getDB();
  const user = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  return user[0]?.credits ?? 0;
}

// 扣除用户积分
export async function deductUserCredits(
  userId: string,
  amount: number
): Promise<boolean> {
  const db = await getDB();

  // 先检查用户是否有足够积分
  const currentCredits = await getUserCredits(userId);
  if (currentCredits < amount) {
    return false;
  }

  // 扣除积分
  await db
    .update(users)
    .set({ credits: currentCredits - amount })
    .where(eq(users.id, userId));

  return true;
}

// 增加用户积分
export async function addUserCredits(
  userId: string,
  amount: number
): Promise<void> {
  const db = await getDB();

  const currentCredits = await getUserCredits(userId);
  await db
    .update(users)
    .set({ credits: currentCredits + amount })
    .where(eq(users.id, userId));
}

// 重置用户积分（用于每月重置）
export async function resetUserCredits(
  userId: string,
  newAmount: number
): Promise<void> {
  const db = await getDB();

  await db
    .update(users)
    .set({
      credits: newAmount,
      creditsResetAt: new Date(),
    })
    .where(eq(users.id, userId));
}

/**
 * 设置用户积分（管理员功能）
 */
export async function setCredits(
  userId: string,
  amount: number
): Promise<number> {
  if (amount < 0) {
    throw new Error("积分数量不能为负数");
  }

  const db = await getDB();
  const result = await db
    .update(users)
    .set({ credits: amount })
    .where(eq(users.id, userId))
    .returning();

  return result[0]?.credits ?? 0;
}

/**
 * 检查用户是否有足够的积分
 */
export async function hasEnoughCredits(
  userId: string,
  requiredAmount: number
): Promise<boolean> {
  const currentCredits = await getUserCredits(userId);
  return currentCredits >= requiredAmount;
}

/**
 * 积分交易记录类型（用于未来扩展）
 */
export type CreditTransaction = {
  type: "add" | "deduct" | "set";
  amount: number;
  description?: string;
  timestamp: Date;
};
