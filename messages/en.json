{"HomePage": {"title": "Get started by editing", "filename": "src/app/page.tsx", "subtitle": "Save and see your changes instantly.", "deploy": "Deploy now", "docs": "Read our docs", "learn": "Learn", "examples": "Examples", "nextjs": "Go to nextjs.org"}, "AboutPage": {"title": "About Us", "subtitle": "Learn more about our company and mission", "description": "We are a technology company focused on building innovative solutions for the modern web. Our team is passionate about creating user-friendly applications that make a difference.", "mission": "Our Mission", "missionText": "To empower developers and businesses with cutting-edge tools and technologies that simplify complex workflows and enhance productivity.", "team": "Our Team", "teamText": "Our diverse team of engineers, designers, and product managers work together to deliver exceptional experiences. We believe in collaboration, innovation, and continuous learning.", "contact": "Get in Touch", "contactText": "Ready to work with us? We'd love to hear from you and discuss how we can help bring your ideas to life.", "backToHome": "Back to Home"}, "Navigation": {"switchToZh": "中文", "switchToEn": "English", "languageLabel": "Language", "home": "Home", "about": "About", "login": "<PERSON><PERSON>"}, "Auth": {"login": {"title": "Sign In", "subtitle": "Welcome back! Please sign in with your Google account.", "googleSignIn": "Continue with Google", "backToHome": "Back to Home"}}, "Subscription": {"success": {"title": "Subscription Successful!", "demoTitle": "Demo Mode", "demoMessage": "This is demo mode, you're seeing what the success page looks like after payment.", "realPaymentMessage": "Thank you for your subscription! Your payment has been successfully processed.", "planDetails": "You have successfully subscribed to the {plan} plan", "backToHome": "Back to Home"}, "plans": {"basic": "Basic", "pro": "Pro", "enterprise": "Enterprise"}}}