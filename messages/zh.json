{"HomePage": {"title": "开始编辑", "filename": "src/app/page.tsx", "subtitle": "保存并立即查看更改。", "deploy": "立即部署", "docs": "阅读文档", "learn": "学习", "examples": "示例", "nextjs": "前往 nextjs.org"}, "AboutPage": {"title": "关于我们", "subtitle": "了解更多关于我们公司和使命的信息", "description": "我们是一家专注于为现代网络构建创新解决方案的科技公司。我们的团队热衷于创建用户友好的应用程序，致力于创造有意义的影响。", "mission": "我们的使命", "missionText": "为开发者和企业提供前沿的工具和技术，简化复杂的工作流程，提高生产力。", "team": "我们的团队", "teamText": "我们由工程师、设计师和产品经理组成的多元化团队共同努力，提供卓越的体验。我们相信协作、创新和持续学习。", "contact": "联系我们", "contactText": "准备与我们合作？我们很乐意听到您的声音，讨论如何帮助您实现想法。", "backToHome": "返回首页"}, "Navigation": {"switchToZh": "中文", "switchToEn": "English", "languageLabel": "语言", "home": "首页", "about": "关于", "login": "登录"}, "Auth": {"login": {"title": "登录", "subtitle": "欢迎回来！请使用您的 Google 账户登录。", "googleSignIn": "使用 Google 继续", "backToHome": "返回首页"}}, "Subscription": {"success": {"title": "订阅成功！", "demoTitle": "演示模式", "demoMessage": "这是演示模式，您看到的是支付成功后的页面效果。", "realPaymentMessage": "感谢您的订阅！您的支付已成功处理。", "planDetails": "您已成功订阅 {plan} 计划", "backToHome": "返回首页"}, "plans": {"basic": "基础版", "pro": "专业版", "enterprise": "企业版"}}}