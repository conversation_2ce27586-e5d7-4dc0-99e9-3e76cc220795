# 项目文档

本目录包含项目的各种技术文档和指南。

## 📚 文档索引

### 数据库管理

- [**数据库管理指南**](./DATABASE_MANAGEMENT.md) - 完整的数据库管理文档，包含详细的工作流程、最佳实践和问题解决方案
- [**数据库快速参考**](./DATABASE_QUICK_REFERENCE.md) - 常用数据库操作命令速查表

### Stripe 集成

- [**Stripe 集成总结**](../STRIPE_INTEGRATION_SUMMARY.md) - Stripe 订阅支付功能的集成总结
- [**Stripe 设置指南**](../STRIPE_SETUP.md) - Stripe 配置和设置步骤

## 🔧 项目技术栈

- **框架**: Next.js 15 with App Router
- **运行时**: Cloudflare Workers
- **数据库**: Cloudflare D1 (SQLite)
- **ORM**: Drizzle ORM
- **认证**: Auth.js (NextAuth.js)
- **支付**: Stripe
- **UI**: shadcn/ui + Tailwind CSS
- **国际化**: next-intl

## 📖 如何使用文档

1. **快速开始**: 查看快速参考文档了解常用操作
2. **深入了解**: 阅读完整的管理指南了解详细流程
3. **问题解决**: 在相应文档的"常见问题"部分查找解决方案
4. **最佳实践**: 遵循文档中的最佳实践建议

## 🆘 获取帮助

如果文档中没有找到答案：

1. 检查项目的 Issues
2. 查看相关技术的官方文档
3. 搜索 Stack Overflow 或相关社区

---

📅 文档维护：请在进行重大更改时及时更新相关文档
