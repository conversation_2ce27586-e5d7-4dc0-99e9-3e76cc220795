# 数据库管理指南

本文档详细说明了项目中 D1 数据库的管理方法，包括迁移、调试和最佳实践。

## 🎯 重要更新：固定数据库路径

本项目现在使用**固定路径的数据库文件**来解决开发体验问题：

- **开发环境**：`local-database/ai-next-template.sqlite` （固定路径）
- **预览环境**：`.wrangler/state/v3/d1/[随机哈希].sqlite` （临时文件）
- **生产环境**：Cloudflare D1 远程数据库

**优势**：

- ✅ 路径固定，便于 DataGrip 等工具连接
- ✅ 开发数据持久保存
- ✅ `npm run dev` 可以正常调试样式
- ✅ 保持与 Cloudflare 的完全兼容性

## 📊 数据库架构

### 当前表结构

- `user` - 用户信息表（包含 credits 积分系统）
- `account` - OAuth 账户信息表
- `session` - 用户会话表
- `verificationToken` - 验证令牌表
- `subscription` - 订阅信息表（Stripe 集成）

### 文件结构

```
migrations/
├── 0000_auth_tables.sql      # 初始的认证表和订阅表
└── meta/
    ├── _journal.json         # 迁移历史记录
    └── 0000_snapshot.json    # 迁移快照

.wrangler/state/v3/d1/miniflare-D1DatabaseObject/
└── ai-next-template.sqlite   # 本地数据库文件
```

## 🔄 数据库变更工作流程

### 场景：修改现有表或添加新表

#### 1. 修改 Schema 文件

在 `src/lib/db/schema.ts` 中添加你的新字段或新表：

```typescript
// 例如：给 user 表增加 avatar 字段
export const users = sqliteTable("user", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: text("name"),
  email: text("email").notNull(),
  emailVerified: integer("emailVerified", { mode: "timestamp_ms" }),
  image: text("image"),
  avatar: text("avatar"), // 新增字段
  credits: integer("credits").notNull().default(0),
  creditsResetAt: integer("creditsResetAt", { mode: "timestamp_ms" }),
});
```

#### 2. 生成新的迁移文件

```bash
LOCAL_DB=true npx drizzle-kit generate
```

#### 3. 应用迁移到本地数据库

```bash
npx wrangler d1 migrations apply ai-next-template --local
```

#### 4. 验证迁移

```bash
sqlite3 .wrangler/state/v3/d1/miniflare-D1DatabaseObject/ai-next-template.sqlite ".schema user"
```

## ⚡ 快捷脚本配置

在 `package.json` 中添加以下脚本：

```json
{
  "scripts": {
    "db:generate": "LOCAL_DB=true npx drizzle-kit generate",
    "db:apply": "npx wrangler d1 migrations apply ai-next-template --local",
    "db:studio": "LOCAL_DB=true npx drizzle-kit studio",
    "db:reset": "rm -rf .wrangler/state/v3/d1 && npm run db:generate && npm run db:apply",
    "db:schema": "sqlite3 .wrangler/state/v3/d1/miniflare-D1DatabaseObject/ai-next-template.sqlite '.schema'",
    "db:backup": "cp .wrangler/state/v3/d1/miniflare-D1DatabaseObject/ai-next-template.sqlite backup_$(date +%Y%m%d_%H%M%S).sqlite"
  }
}
```

### 使用方式：

```bash
npm run db:generate  # 生成迁移文件
npm run db:apply     # 应用迁移到本地
npm run db:studio    # 打开数据库可视化界面
npm run db:schema    # 查看数据库结构
npm run db:backup    # 备份当前数据库
npm run db:reset     # 重置数据库（谨慎使用）
```

## 🔍 调试和检查命令

### 查看迁移状态

```bash
# 查看哪些迁移已应用
npx wrangler d1 migrations list ai-next-template --local
```

### 查看数据库结构

```bash
# 查看所有表结构
sqlite3 local-database/ai-next-template.sqlite ".schema"

# 查看特定表结构
sqlite3 local-database/ai-next-template.sqlite ".schema user"
```

### 查看数据

```bash
# 查看表中的数据
sqlite3 local-database/ai-next-template.sqlite "SELECT * FROM user LIMIT 5;"

# 查看表的记录数
sqlite3 local-database/ai-next-template.sqlite "SELECT COUNT(*) FROM user;"
```

## 🚨 数据库重置（谨慎使用）

### 何时使用重置

- ✅ 开发早期阶段，没有重要数据
- ✅ 本地测试环境，数据可以丢失
- ✅ 迁移文件出现严重冲突
- ❌ 生产环境（永远不要）
- ❌ 有重要测试数据时
- ❌ 团队协作项目

### 完整重置步骤

```bash
# 1. 停止开发服务器
pkill -f "next dev"

# 2. 删除本地数据库
rm -rf local-database/

# 3. 重新生成迁移（如果需要）
npm run db:generate

# 4. 创建新的数据库并应用迁移
npm run preview &
sleep 10
npm run db:apply
pkill -f wrangler

# 5. 迁移到固定位置
npm run db:migrate
```

或使用快捷脚本：

```bash
npm run db:reset
```

## 🚀 生产环境部署

### 部署流程

```bash
# 1. 确保本地迁移测试通过
npm run db:generate
npm run db:apply

# 2. 提交代码
git add migrations/
git commit -m "feat: add new database schema changes"
git push

# 3. 部署到生产环境
npx wrangler d1 migrations apply ai-next-template --remote
npx wrangler deploy
```

## 💾 数据备份和恢复

### 备份本地数据库

```bash
# 手动备份
cp local-database/ai-next-template.sqlite backup_$(date +%Y%m%d_%H%M%S).sqlite

# 使用脚本备份
npm run db:backup
```

### 恢复数据库

```bash
# 从备份恢复
cp backup_20240101_120000.sqlite local-database/ai-next-template.sqlite
```

## 🔧 配置文件说明

### drizzle.config.ts

该文件配置了 Drizzle ORM 的行为：

- 本地开发时使用 SQLite 文件
- 生产环境使用 Cloudflare D1 HTTP API
- 自动从 `wrangler.jsonc` 读取数据库名称

### wrangler.jsonc

包含 Cloudflare Workers 和 D1 数据库的配置：

```json
{
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "ai-next-template",
      "database_id": "1c23afe7-6c5e-477a-bca3-e8602fcc904f"
    }
  ]
}
```

## 🐛 常见问题解决

### 问题：迁移文件不能应用

```bash
# 检查数据库文件是否存在
ls -la local-database/

# 重新启动开发服务器
pkill -f "next dev"
npm run dev
```

### 问题：数据库连接失败

```bash
# 检查配置文件
cat drizzle.config.ts
cat wrangler.jsonc

# 验证服务器状态
curl -I http://localhost:8787
```

### 问题：Auth.js 认证失败

通常是数据库表结构问题：

```bash
# 检查必需的认证表是否存在
sqlite3 local-database/ai-next-template.sqlite ".tables"

# 应该看到：account session user verificationToken
```

## 📝 最佳实践

### 开发阶段

1. **增量更新**：优先使用迁移而不是重置
2. **频繁备份**：重要更改前先备份
3. **测试迁移**：本地测试通过后再推送

### 生产环境

1. **谨慎操作**：永远不要删除生产数据
2. **版本控制**：所有迁移文件都要提交到 git
3. **监控日志**：部署后检查应用日志

### 团队协作

1. **同步迁移**：团队成员及时拉取最新迁移
2. **冲突解决**：迁移冲突时沟通解决方案
3. **文档更新**：数据库变更要更新相关文档

---

📅 最后更新：2024 年 1 月
👤 维护者：项目团队
