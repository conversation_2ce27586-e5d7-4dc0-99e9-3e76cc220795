# Stripe 订阅集成总结

## ✅ 已完成的工作

### 1. 数据库结构

- ✅ 新增 `subscriptions` 表
- ✅ 为 `users` 表添加 `creditsResetAt` 字段
- ✅ 生成数据库迁移文件

### 2. 环境配置

- ✅ 在 `.dev.vars` 中添加 Stripe 环境变量
- ✅ 更新 `CloudflareEnv` 类型定义
- ✅ 适配 Cloudflare Workers 环境

### 3. Stripe 配置

- ✅ 安装 Stripe SDK
- ✅ 创建适配 Cloudflare Workers 的 Stripe 配置
- ✅ 定义三种订阅计划（Basic/Pro/Premium）

### 4. 前端页面

- ✅ 订阅计划选择页面 (`/subscription/plans`)
- ✅ 订阅成功页面 (`/subscription/success`)
- ✅ 响应式设计

### 5. 文档

- ✅ 详细的配置指南 (`STRIPE_SETUP.md`)

## 🔧 接下来需要做的

### 1. 在 `.dev.vars` 中配置真实的 Stripe 密钥

```bash
# 替换为你的真实 Stripe 密钥
STRIPE_SECRET_KEY=sk_test_your_actual_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_key
STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret

# 替换为在 Stripe Dashboard 中创建的价格 ID
STRIPE_BASIC_PRICE_ID=price_actual_basic_id
STRIPE_PRO_PRICE_ID=price_actual_pro_id
STRIPE_PREMIUM_PRICE_ID=price_actual_premium_id
```

### 2. 应用数据库迁移

```bash
npx drizzle-kit push
```

### 3. 实现 API 路由

你需要创建以下 API 路由：

#### `/src/app/api/stripe/checkout/route.ts`

```typescript
import { NextRequest } from "next/server";
import { createStripe, getSubscriptionPlans } from "@/lib/stripe";

export async function POST(req: NextRequest, { env }: { env: CloudflareEnv }) {
  const stripe = createStripe(env);
  const plans = getSubscriptionPlans(env);

  // 处理创建 checkout session 的逻辑
}
```

#### `/src/app/api/stripe/webhook/route.ts`

```typescript
import { NextRequest } from "next/server";
import { createStripe } from "@/lib/stripe";

export async function POST(req: NextRequest, { env }: { env: CloudflareEnv }) {
  const stripe = createStripe(env);

  // 处理 webhook 事件的逻辑
}
```

### 4. 在 Stripe Dashboard 中配置

1. 创建三个订阅产品和对应的价格
2. 设置 webhook 端点
3. 获取相关的 ID 和密钥

## 📋 关键特性

- **按月订阅**：三种计划 $9.99/$19.99/$49.99
- **积分管理**：每月 1 号自动重置，剩余积分清零
- **取消订阅**：积分保留继续使用
- **Cloudflare Workers 兼容**：完全适配 CF Workers 环境

## 🎯 核心文件

- `src/lib/stripe.ts` - Stripe 配置和工具函数
- `src/lib/db/schema.ts` - 数据库表定义
- `src/app/[locale]/(main)/subscription/plans/page.tsx` - 订阅计划页面
- `.dev.vars` - 开发环境变量
- `cloudflare-env.d.ts` - TypeScript 类型定义

现在项目的基础架构已经完成，你只需要填入真实的 Stripe 配置并实现 API 路由就可以开始使用了！
