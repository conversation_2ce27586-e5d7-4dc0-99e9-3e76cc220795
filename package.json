{"name": "ai-next-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "node scripts/preview-with-db.js", "preview:simple": "opennextjs-cloudflare build && opennextjs-cloudflare preview --port 8787", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "db:generate": "LOCAL_DB=true npx drizzle-kit generate", "db:apply": "npx wrangler d1 migrations apply ai-next-template --local", "db:apply:remote": "npx wrangler d1 migrations apply ai-next-template --remote", "db:studio": "LOCAL_DB=true drizzle-kit studio", "db:studio:local": "node scripts/ensure-local-db.js && drizzle-kit studio --config=drizzle.local.config.ts", "db:studio:refresh": "pkill -f 'drizzle-kit studio' || true && npm run db:studio:local", "db:studio:remote": "DATABASE_ID=1c23afe7-6c5e-477a-bca3-e8602fcc904f drizzle-kit studio", "db:reset": "node scripts/reset-db.js", "db:reset:studio": "npm run db:reset && npm run db:studio:refresh", "db:clean": "node scripts/clean-db.js", "db:migrate": "node scripts/migrate-db.js", "db:schema": "sqlite3 local-database/ai-next-template.sqlite '.schema'", "db:backup": "cp local-database/ai-next-template.sqlite backup_$(date +%Y%m%d_%H%M%S).sqlite", "db:list": "npx wrangler d1 migrations list ai-next-template --local"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@cloudflare/workers-types": "^4.20250628.0", "@hookform/resolvers": "^5.1.1", "@opennextjs/cloudflare": "^1.3.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.20.2", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^5.0.0-beta.29", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "phosphor-react": "^1.4.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/better-sqlite3": "^7.6.13", "@types/glob": "^8.1.0", "@types/node": "^20.19.2", "@types/react": "^19", "@types/react-dom": "^19", "better-sqlite3": "^12.2.0", "eslint": "^9", "eslint-config-next": "15.3.4", "glob": "^11.0.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "wrangler": "^4.22.0"}}