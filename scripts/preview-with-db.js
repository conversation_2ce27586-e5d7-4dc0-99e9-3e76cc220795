#!/usr/bin/env node

import { spawn, execSync } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

/**
 * 智能预览脚本
 * 1. 构建应用
 * 2. 检查数据库是否需要迁移
 * 3. 自动应用迁移（如果需要）
 * 4. 启动预览服务器
 */

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 执行命令: ${command} ${args.join(" ")}`);

    const process = spawn(command, args, {
      cwd: projectRoot,
      stdio: "inherit",
      ...options,
    });

    process.on("exit", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令失败，退出代码: ${code}`));
      }
    });
  });
}

function checkDatabaseNeedsMigration() {
  const wranglerDbDir = path.join(
    projectRoot,
    ".wrangler/state/v3/d1/miniflare-D1DatabaseObject"
  );

  if (!fs.existsSync(wranglerDbDir)) {
    console.log("📋 数据库目录不存在，需要迁移");
    return true;
  }

  const files = fs
    .readdirSync(wranglerDbDir)
    .filter(
      (file) =>
        file.endsWith(".sqlite") &&
        !file.endsWith(".sqlite-shm") &&
        !file.endsWith(".sqlite-wal")
    );

  if (files.length === 0) {
    console.log("📋 未找到数据库文件，需要迁移");
    return true;
  }

  // 检查数据库是否有表结构
  const dbFile = path.join(wranglerDbDir, files[0]);
  try {
    const result = execSync(`sqlite3 "${dbFile}" ".tables"`, {
      encoding: "utf8",
      cwd: projectRoot,
    });

    if (!result.trim() || !result.includes("user")) {
      console.log("📋 数据库文件存在但缺少表结构，需要迁移");
      return true;
    }

    console.log("✅ 数据库已存在且有表结构");
    return false;
  } catch (error) {
    console.log("📋 检查数据库时出错，需要迁移");
    return true;
  }
}

async function applyMigrationWithConfirmation() {
  console.log("📋 应用数据库迁移...");

  return new Promise((resolve, reject) => {
    const process = spawn("npm", ["run", "db:apply"], {
      cwd: projectRoot,
      stdio: ["pipe", "inherit", "inherit"],
    });

    // 自动回答 "y" 确认迁移
    process.stdin.write("y\n");
    process.stdin.end();

    process.on("exit", (code) => {
      if (code === 0) {
        console.log("✅ 数据库迁移完成");
        resolve();
      } else {
        reject(new Error(`迁移失败，退出代码: ${code}`));
      }
    });
  });
}

async function main() {
  try {
    console.log("🏗️ 开始构建和预览流程...\n");

    // 1. 构建应用
    console.log("📦 构建 Next.js 应用...");
    await runCommand("npx", ["opennextjs-cloudflare", "build"]);

    // 2. 检查是否需要迁移
    console.log("\n🔍 检查数据库状态...");
    const needsMigration = await checkDatabaseNeedsMigration();

    // 3. 如果需要，应用迁移
    if (needsMigration) {
      await applyMigrationWithConfirmation();
    }

    // 4. 启动预览服务器
    console.log("\n🚀 启动预览服务器...");
    await runCommand("npx", [
      "opennextjs-cloudflare",
      "preview",
      "--port",
      "8787",
    ]);
  } catch (error) {
    console.error("❌ 预览流程失败:", error.message);
    process.exit(1);
  }
}

main();
