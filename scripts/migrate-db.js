import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 数据库迁移脚本
 * 将 wrangler 生成的随机名称数据库文件迁移到固定位置
 */
function migrateDatabase() {
  const projectRoot = path.dirname(__dirname);
  const wranglerDbDir = path.join(
    projectRoot,
    ".wrangler/state/v3/d1/miniflare-D1DatabaseObject"
  );
  const fixedDbPath = path.join(
    projectRoot,
    "local-database",
    "ai-next-template.sqlite"
  );

  console.log("🔍 正在查找现有数据库文件...");

  try {
    if (!fs.existsSync(wranglerDbDir)) {
      console.log("❌ 未找到 wrangler 数据库目录");
      return;
    }

    // 查找所有有效的数据库文件
    const files = fs.readdirSync(wranglerDbDir).filter(
      (file) =>
        file.endsWith(".sqlite") &&
        !file.endsWith(".sqlite-shm") &&
        !file.endsWith(".sqlite-wal") &&
        fs.statSync(path.join(wranglerDbDir, file)).size > 1024 // 至少1KB，确保有实际内容
    );

    if (files.length === 0) {
      console.log("❌ 未找到有效的数据库文件");
      return;
    }

    console.log(`📁 找到 ${files.length} 个数据库文件:`);
    files.forEach((file, index) => {
      const filePath = path.join(wranglerDbDir, file);
      const size = (fs.statSync(filePath).size / 1024).toFixed(2);
      console.log(`  ${index + 1}. ${file} (${size} KB)`);
    });

    // 使用最大的文件（通常包含最多数据）
    const largestFile = files.reduce((largest, current) => {
      const currentSize = fs.statSync(path.join(wranglerDbDir, current)).size;
      const largestSize = fs.statSync(path.join(wranglerDbDir, largest)).size;
      return currentSize > largestSize ? current : largest;
    });

    const sourceDbPath = path.join(wranglerDbDir, largestFile);

    console.log(`🎯 选择迁移文件: ${largestFile}`);

    // 创建目标目录
    const targetDir = path.dirname(fixedDbPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`📁 创建目录: ${path.relative(projectRoot, targetDir)}`);
    }

    // 如果目标文件已存在，创建备份
    if (fs.existsSync(fixedDbPath)) {
      const backupPath = `${fixedDbPath}.backup.${Date.now()}`;
      fs.copyFileSync(fixedDbPath, backupPath);
      console.log(`💾 备份现有数据库到: ${path.basename(backupPath)}`);
    }

    // 复制数据库文件
    fs.copyFileSync(sourceDbPath, fixedDbPath);

    const sourceSize = (fs.statSync(sourceDbPath).size / 1024).toFixed(2);
    const targetSize = (fs.statSync(fixedDbPath).size / 1024).toFixed(2);

    console.log(`✅ 迁移完成!`);
    console.log(`   源文件: ${largestFile} (${sourceSize} KB)`);
    console.log(
      `   目标文件: ${path.relative(
        projectRoot,
        fixedDbPath
      )} (${targetSize} KB)`
    );

    // 显示清理建议
    console.log(`\n🧹 建议清理旧文件:`);
    console.log(`   rm -rf ${path.relative(projectRoot, wranglerDbDir)}`);
  } catch (error) {
    console.error("❌ 迁移过程中出错:", error.message);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateDatabase();
}

export { migrateDatabase };
