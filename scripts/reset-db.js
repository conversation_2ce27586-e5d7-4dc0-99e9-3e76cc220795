import fs from "fs";
import path from "path";
import { spawn } from "child_process";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

/**
 * 数据库重置脚本
 * 完全重置本地数据库并重新创建表结构
 */
async function resetDatabase() {
  console.log("🔄 开始重置数据库...");

  try {
    // 0. 检查并停止可能运行的 Drizzle Studio 进程
    console.log("🔍 检查运行中的 Drizzle Studio 进程...");
    try {
      const { execSync } = await import("child_process");
      // 查找 drizzle-kit studio 进程
      const result = execSync("pgrep -f 'drizzle-kit studio'", {
        encoding: "utf8",
        stdio: "pipe",
      });

      if (result.trim()) {
        console.log("⏹️ 发现运行中的 Drizzle Studio，正在停止...");
        execSync("pkill -f 'drizzle-kit studio'", { stdio: "pipe" });
        // 等待进程完全停止
        await new Promise((resolve) => setTimeout(resolve, 2000));
        console.log("✅ Drizzle Studio 进程已停止");
      }
    } catch (error) {
      // 没有找到进程或停止失败，继续执行
      console.log("ℹ️ 没有发现运行中的 Drizzle Studio 进程");
    }
    // 1. 强制关闭所有 SQLite 连接并删除现有的固定数据库
    const localDbPath = path.join(projectRoot, "local-database");
    const mainDbFile = path.join(localDbPath, "ai-next-template.sqlite");

    if (fs.existsSync(localDbPath)) {
      // 强制同步 WAL 文件到主数据库，然后删除
      if (fs.existsSync(mainDbFile)) {
        try {
          const { execSync } = await import("child_process");
          console.log("🔄 强制同步并关闭 WAL 文件...");
          execSync(
            `sqlite3 "${mainDbFile}" "PRAGMA wal_checkpoint(TRUNCATE); PRAGMA journal_mode=DELETE;"`,
            {
              stdio: "pipe",
            }
          );
        } catch (error) {
          console.log("⚠️ WAL 同步失败，强制删除文件");
        }
      }

      fs.rmSync(localDbPath, { recursive: true, force: true });
      console.log("🗑️ 删除现有数据库文件（包括 WAL 和 SHM 文件）");
    }

    // 2. 删除 wrangler 数据库缓存
    const wranglerDbPath = path.join(projectRoot, ".wrangler/state/v3/d1");
    if (fs.existsSync(wranglerDbPath)) {
      fs.rmSync(wranglerDbPath, { recursive: true, force: true });
      console.log("🧹 清理 wrangler 缓存");
    }

    console.log("⏳ 启动预览服务器创建新数据库...");

    // 3. 启动预览服务器（会创建新的数据库）
    const previewProcess = spawn("npm", ["run", "preview"], {
      cwd: projectRoot,
      stdio: "pipe",
    });

    // 等待服务器启动
    await new Promise((resolve) => setTimeout(resolve, 15000));

    console.log("📋 应用数据库迁移...");

    // 4. 应用迁移（自动回答 y）
    const applyProcess = spawn("npm", ["run", "db:apply"], {
      cwd: projectRoot,
      stdio: ["pipe", "inherit", "inherit"],
    });

    // 自动发送 "y" 回答确认
    applyProcess.stdin.write("y\n");
    applyProcess.stdin.end();

    await new Promise((resolve, reject) => {
      applyProcess.on("exit", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`迁移失败，退出代码: ${code}`));
        }
      });
    });

    // 5. 停止预览服务器
    previewProcess.kill("SIGTERM");

    // 等待一下确保进程结束和数据库写入完成
    await new Promise((resolve) => setTimeout(resolve, 5000));

    console.log("🔄 迁移到固定位置...");

    // 6. 自动查找并复制数据库文件到固定位置
    const wranglerDbDir = path.join(
      projectRoot,
      ".wrangler/state/v3/d1/miniflare-D1DatabaseObject"
    );
    const fixedDbPath = path.join(
      projectRoot,
      "local-database",
      "ai-next-template.sqlite"
    );

    if (fs.existsSync(wranglerDbDir)) {
      console.log(`📁 检查目录: ${wranglerDbDir}`);

      // 查找所有 .sqlite 文件
      const allFiles = fs.readdirSync(wranglerDbDir);
      console.log(`📄 找到文件: ${allFiles.join(", ")}`);

      // 查找有效的数据库文件
      const files = fs
        .readdirSync(wranglerDbDir)
        .filter(
          (file) =>
            file.endsWith(".sqlite") &&
            !file.endsWith(".sqlite-shm") &&
            !file.endsWith(".sqlite-wal")
        );

      console.log(`🔍 SQLite 文件: ${files.join(", ")}`);

      // 检查文件大小
      const validFiles = files.filter((file) => {
        const filePath = path.join(wranglerDbDir, file);
        const size = fs.statSync(filePath).size;
        console.log(`📊 ${file}: ${size} bytes`);
        return size > 1024; // 降低到1KB
      });

      if (validFiles.length > 0) {
        // 使用最大的文件（通常包含最多数据）
        const largestFile = validFiles.reduce((largest, current) => {
          const currentSize = fs.statSync(
            path.join(wranglerDbDir, current)
          ).size;
          const largestSize = fs.statSync(
            path.join(wranglerDbDir, largest)
          ).size;
          return currentSize > largestSize ? current : largest;
        });

        const sourceDbPath = path.join(wranglerDbDir, largestFile);

        // 创建固定数据库目录
        const fixedDir = path.dirname(fixedDbPath);
        if (!fs.existsSync(fixedDir)) {
          fs.mkdirSync(fixedDir, { recursive: true });
        }

        // 复制数据库文件
        fs.copyFileSync(sourceDbPath, fixedDbPath);

        // 如果存在 WAL 和 SHM 文件，也复制它们
        const walFile = sourceDbPath + "-wal";
        const shmFile = sourceDbPath + "-shm";

        if (fs.existsSync(walFile)) {
          fs.copyFileSync(walFile, fixedDbPath + "-wal");
          console.log("📋 复制 WAL 文件");
        }

        if (fs.existsSync(shmFile)) {
          fs.copyFileSync(shmFile, fixedDbPath + "-shm");
          console.log("📋 复制 SHM 文件");
        }

        // 强制同步 WAL 数据到主文件
        try {
          const { execSync } = await import("child_process");
          execSync(`sqlite3 "${fixedDbPath}" "PRAGMA wal_checkpoint(FULL);"`, {
            stdio: "pipe",
          });
          console.log("🔄 强制同步 WAL 数据");
        } catch {
          console.log("⚠️ WAL 同步可能失败，但继续执行");
        }

        const sourceSize = (fs.statSync(sourceDbPath).size / 1024).toFixed(2);
        const targetSize = (fs.statSync(fixedDbPath).size / 1024).toFixed(2);

        console.log(`✅ 数据库迁移完成!`);
        console.log(`   源文件: ${largestFile} (${sourceSize} KB)`);
        console.log(
          `   目标文件: local-database/ai-next-template.sqlite (${targetSize} KB)`
        );

        // 验证表结构
        const { spawn: spawnSync } = await import("child_process");
        try {
          const result = spawnSync("sqlite3", [fixedDbPath, ".tables"], {
            encoding: "utf8",
            stdio: "pipe",
          });

          if (result.stdout && result.stdout.trim()) {
            console.log(
              `📋 数据库表: ${result.stdout.trim().replace(/\s+/g, ", ")}`
            );
          }
        } catch {
          console.log("📋 数据库已创建（无法验证表结构）");
        }

        // 清理旧的 wrangler 文件
        fs.rmSync(wranglerDbDir, { recursive: true, force: true });
        console.log("🧹 清理旧的 wrangler 数据库文件");
      } else {
        throw new Error("未找到有效的数据库文件");
      }
    } else {
      throw new Error("未找到 wrangler 数据库目录");
    }

    console.log("✅ 数据库重置完成!");
    console.log("📂 新数据库位置: local-database/ai-next-template.sqlite");
    console.log("");
    console.log("🚀 接下来您可以:");
    console.log("   npm run dev              # 开始开发");
    console.log("   npm run db:studio:local  # 打开数据库管理界面");
    console.log("");
    console.log("💡 如果 Drizzle Studio 仍显示旧数据，请:");
    console.log("   1. 重新启动 Drizzle Studio");
    console.log("   2. 或在浏览器中强制刷新 (Ctrl+Shift+R / Cmd+Shift+R)");
  } catch (error) {
    console.error("❌ 重置过程中出错:", error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  resetDatabase();
}

export { resetDatabase };
