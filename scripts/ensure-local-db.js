#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

/**
 * 确保本地数据库文件存在
 * 如果不存在，则创建一个空的数据库文件
 */
function ensureLocalDatabase() {
  const localDbPath = path.join(projectRoot, "local-database");
  const dbFile = path.join(localDbPath, "ai-next-template.sqlite");

  // 检查数据库文件是否存在
  if (fs.existsSync(dbFile)) {
    console.log("✅ 本地数据库文件已存在");
    return;
  }

  console.log("📁 本地数据库文件不存在，正在创建...");

  // 创建目录
  if (!fs.existsSync(localDbPath)) {
    fs.mkdirSync(localDbPath, { recursive: true });
    console.log("📁 创建 local-database 目录");
  }

  // 创建空的数据库文件
  fs.writeFileSync(dbFile, "");
  console.log("📝 创建空的数据库文件");

  console.log("💡 提示: 请运行以下命令来创建表结构:");
  console.log("   npm run dev  # 启动开发模式会自动创建表结构");
  console.log("   或者手动应用迁移:");
  console.log("   npm run db:apply");
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  ensureLocalDatabase();
}

export { ensureLocalDatabase };
