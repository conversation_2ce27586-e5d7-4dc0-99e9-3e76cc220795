#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

/**
 * 彻底清理数据库脚本
 * 清理所有 SQLite 文件，包括 WAL 和 SHM 文件
 */
function cleanDatabase() {
  console.log("🧹 开始彻底清理数据库...");

  try {
    // 1. 清理固定位置的数据库
    const localDbPath = path.join(projectRoot, "local-database");
    const mainDbFile = path.join(localDbPath, "ai-next-template.sqlite");

    if (fs.existsSync(mainDbFile)) {
      console.log("🔄 关闭 WAL 模式并同步数据...");
      try {
        // 强制关闭 WAL 模式，将所有数据写入主文件
        execSync(
          `sqlite3 "${mainDbFile}" "PRAGMA wal_checkpoint(TRUNCATE); PRAGMA journal_mode=DELETE;"`,
          { stdio: "pipe" }
        );
      } catch (error) {
        console.log("⚠️ WAL 同步失败，继续删除文件");
      }
    }

    if (fs.existsSync(localDbPath)) {
      fs.rmSync(localDbPath, { recursive: true, force: true });
      console.log("🗑️ 删除 local-database 目录");
    }

    // 2. 清理 wrangler 数据库
    const wranglerDbPath = path.join(projectRoot, ".wrangler/state/v3/d1");
    if (fs.existsSync(wranglerDbPath)) {
      fs.rmSync(wranglerDbPath, { recursive: true, force: true });
      console.log("🗑️ 删除 wrangler 数据库缓存");
    }

    // 3. 查找并删除任何遗留的 SQLite 文件
    console.log("🔍 查找遗留的 SQLite 文件...");
    try {
      const result = execSync("find . -name '*.sqlite*' -not -path './node_modules/*'", {
        encoding: "utf8",
        cwd: projectRoot,
      });

      if (result.trim()) {
        const files = result.trim().split("\n");
        console.log(`📁 找到 ${files.length} 个 SQLite 文件:`);
        files.forEach((file) => {
          console.log(`   ${file}`);
          try {
            fs.unlinkSync(path.join(projectRoot, file));
            console.log(`   ✅ 删除: ${file}`);
          } catch (error) {
            console.log(`   ❌ 删除失败: ${file}`);
          }
        });
      } else {
        console.log("✅ 没有找到遗留的 SQLite 文件");
      }
    } catch (error) {
      console.log("⚠️ 查找 SQLite 文件时出错，但继续执行");
    }

    console.log("✅ 数据库清理完成!");
    console.log("💡 现在可以运行以下命令重新创建数据库:");
    console.log("   npm run dev  # 开发模式");
    console.log("   npm run preview  # 预览模式");

  } catch (error) {
    console.error("❌ 清理过程中出错:", error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanDatabase();
}

export { cleanDatabase };
