#!/usr/bin/env node

import { execSync } from "child_process";

/**
 * 刷新 Drizzle Studio 脚本
 * 停止当前运行的 Drizzle Studio 并重新启动
 */
async function refreshStudio() {
  console.log("🔄 刷新 Drizzle Studio...");

  try {
    // 1. 停止现有的 Drizzle Studio 进程
    console.log("⏹️ 停止现有的 Drizzle Studio 进程...");
    try {
      execSync("pkill -f 'drizzle-kit studio'", { stdio: "pipe" });
      console.log("✅ 已停止现有进程");
    } catch (error) {
      console.log("ℹ️ 没有发现运行中的 Drizzle Studio 进程");
    }

    // 等待进程完全停止
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 2. 重新启动 Drizzle Studio
    console.log("🚀 重新启动 Drizzle Studio...");
    console.log("💡 Drizzle Studio 将在新的终端窗口中启动");
    
    // 在后台启动 Drizzle Studio
    execSync("npm run db:studio:local", { 
      stdio: "inherit",
      detached: true 
    });

  } catch (error) {
    console.error("❌ 刷新过程中出错:", error.message);
    console.log("💡 请手动重新启动 Drizzle Studio:");
    console.log("   npm run db:studio:local");
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  refreshStudio();
}

export { refreshStudio };
