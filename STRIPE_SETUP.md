# Stripe 订阅集成配置指南

## 🚀 快速开始

本项目已集成 Stripe 订阅支付功能，支持按月订阅和积分自动发放。

## 📋 功能特性

- ✅ 三种订阅计划：Basic ($9.99)、Pro ($19.99)、Premium ($49.99)
- ✅ 每月 1 号自动重置积分
- ✅ 上月剩余积分清零
- ✅ 取消订阅后积分继续使用
- ✅ Webhook 自动处理订阅状态
- ✅ 响应式订阅页面

## 🔧 Stripe 配置步骤

### 1. 创建 Stripe 账户和获取密钥

1. 访问 [Stripe Dashboard](https://dashboard.stripe.com)
2. 在开发者 > API 密钥中获取：
   - 可发布密钥 (pk*test*...)
   - 密钥 (sk*test*...)

### 2. 创建订阅产品和价格

在 Stripe Dashboard 中：

1. 产品 > 创建产品
2. 为每个计划创建产品：
   - **Basic Plan**: $9.99/月
   - **Pro Plan**: $19.99/月
   - **Premium Plan**: $49.99/月
3. 记录每个价格的 ID (price\_...)

### 3. 设置 Webhook

1. 开发者 > Webhooks > 添加端点
2. 端点 URL: `https://yourdomain.com/api/stripe/webhook`
3. 选择事件：
   - `checkout.session.completed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
4. 记录 Webhook 签名密钥 (whsec\_...)

### 4. 环境变量配置

在 Cloudflare Workers 项目中，开发环境变量需要添加到 `.dev.vars` 文件：

```bash
# 在 .dev.vars 文件中添加以下内容
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# 订阅计划价格 ID（需要在 Stripe Dashboard 中创建后填入）
STRIPE_BASIC_PRICE_ID=price_your_basic_price_id_here
STRIPE_PRO_PRICE_ID=price_your_pro_price_id_here
STRIPE_PREMIUM_PRICE_ID=price_your_premium_price_id_here
```

对于生产环境，需要在 Cloudflare Dashboard 中配置相应的环境变量，或使用 `wrangler secret put` 命令：

```bash
# 设置生产环境密钥
wrangler secret put STRIPE_SECRET_KEY
wrangler secret put STRIPE_WEBHOOK_SECRET

# 设置生产环境变量
wrangler env put STRIPE_PUBLISHABLE_KEY pk_live_...
wrangler env put STRIPE_BASIC_PRICE_ID price_...
wrangler env put STRIPE_PRO_PRICE_ID price_...
wrangler env put STRIPE_PREMIUM_PRICE_ID price_...
```

## 📊 数据库结构

### 新增的数据库表

#### subscriptions 表

```sql
CREATE TABLE subscription (
  id TEXT PRIMARY KEY,
  userId TEXT NOT NULL,
  stripeCustomerId TEXT NOT NULL,
  stripeSubscriptionId TEXT NOT NULL,
  stripePriceId TEXT NOT NULL,
  status TEXT NOT NULL,
  planType TEXT NOT NULL,
  creditsPerMonth INTEGER NOT NULL,
  currentPeriodStart INTEGER NOT NULL,
  currentPeriodEnd INTEGER NOT NULL,
  createdAt INTEGER NOT NULL,
  updatedAt INTEGER NOT NULL
);
```

#### users 表更新

```sql
-- 新增字段
ALTER TABLE user ADD COLUMN creditsResetAt INTEGER;
```

## 🛠️ API 路由

### `/api/stripe/checkout` (POST)

创建 Stripe Checkout 会话

**请求体：**

```json
{
  "planType": "basic" | "pro" | "premium"
}
```

**响应：**

```json
{
  "checkoutUrl": "https://checkout.stripe.com/..."
}
```

### `/api/stripe/webhook` (POST)

处理 Stripe Webhook 事件

- 自动创建/更新订阅记录
- 发放积分到用户账户
- 处理订阅状态变更

## 📱 前端页面

### `/subscription/plans`

订阅计划选择页面

- 展示三种计划的特性和价格
- 一键跳转到 Stripe Checkout

### `/subscription/success`

订阅成功页面

- 支付完成后的确认页面
- 指导用户下一步操作

## 🔄 积分发放逻辑

1. **首次订阅**：支付成功后立即发放积分
2. **每月续费**：每月 1 号自动重置积分
3. **取消订阅**：积分保留，不再续费

## 🧪 测试

### Stripe 测试卡号

```
4242 4242 4242 4242 - Visa (成功)
4000 0000 0000 0002 - 卡被拒绝
4000 0000 0000 9995 - 资金不足
```

### 测试步骤

1. 使用测试环境的 API 密钥
2. 访问 `/subscription/plans`
3. 选择计划并使用测试卡号
4. 验证 Webhook 事件处理
5. 检查数据库中的订阅和积分记录

## ⚠️ 注意事项

1. **生产环境**：确保使用生产环境的 Stripe 密钥
2. **HTTPS**：Webhook 需要 HTTPS 端点
3. **时区**：积分重置基于服务器时区
4. **错误处理**：监控 Webhook 事件和支付失败
5. **安全性**：验证 Webhook 签名

## 🆘 故障排除

### 常见问题

1. **Webhook 未触发**

   - 检查端点 URL 是否正确
   - 验证 HTTPS 配置
   - 查看 Stripe Dashboard 中的 Webhook 日志

2. **积分未发放**

   - 检查 Webhook 事件处理逻辑
   - 验证数据库连接
   - 查看服务器日志

3. **订阅状态同步问题**
   - 检查 Webhook 签名验证
   - 确认事件类型处理
   - 验证数据库更新操作

## 📞 支持

如果在集成过程中遇到问题，请：

1. 查看 Stripe Dashboard 中的日志
2. 检查服务器错误日志
3. 参考 [Stripe 官方文档](https://stripe.com/docs)

---

现在你的项目已经支持 Stripe 订阅支付功能！🎉
