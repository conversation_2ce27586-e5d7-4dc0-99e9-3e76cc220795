import type { Config } from "drizzle-kit";
import fs from "fs";

// 为本地开发创建一个单独的配置
const isLocal =
  process.env.NODE_ENV === "development" || process.env.LOCAL_DB === "true";

// 从 wrangler.jsonc 读取数据库名称
const getDbNameFromWrangler = () => {
  try {
    const wranglerConfig = JSON.parse(
      fs.readFileSync("wrangler.jsonc", "utf-8")
    );
    const dbConfig = wranglerConfig.d1_databases?.[0];
    return dbConfig?.database_name || "ai-next-template";
  } catch {
    console.warn("无法读取 wrangler.jsonc，使用默认数据库名称");
    return "ai-next-template";
  }
};

export default {
  schema: "./src/lib/db/schema.ts",
  out: "./migrations",
  dialect: "sqlite",
  dbCredentials: {
    url: isLocal
      ? `.wrangler/state/v3/d1/miniflare-D1DatabaseObject/${getDbNameFromWrangler()}.sqlite`
      : process.env.D1_DATABASE_URL || "",
  },
  // verbose: true,
  // strict: true,
} satisfies Config;
